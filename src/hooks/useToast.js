import { useState, useCallback } from 'react'

// Простая система уведомлений для проекта
export const useToast = () => {
  const [toasts, setToasts] = useState([])

  const addToast = useCallback((toast) => {
    const id = Date.now() + Math.random()
    const newToast = { id, ...toast }
    
    setToasts(prev => [...prev, newToast])
    
    // Автоматически удаляем toast через 5 секунд
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id))
    }, 5000)
    
    return id
  }, [])

  const removeToast = useCallback((id) => {
    setToasts(prev => prev.filter(t => t.id !== id))
  }, [])

  const success = useCallback((options) => {
    return addToast({ type: 'success', ...options })
  }, [addToast])

  const error = useCallback((options) => {
    return addToast({ type: 'error', ...options })
  }, [addToast])

  const info = useCallback((options) => {
    return addToast({ type: 'info', ...options })
  }, [addToast])

  return {
    toasts,
    success,
    error,
    info,
    removeToast
  }
}
