import { useSelector } from 'react-redux'

import { getEventFormats } from '@/reducer/memberRegistration/selectors'

import styles from './FormatSelector.module.scss'

const FormatSelector = ({ selectedFormatId, onFormatChange }) => {
  // Используем форматы событий, которые уже загружены в ReservedNumbers
  const formats = useSelector((state) => getEventFormats(state))

  const handleFormatChange = (event) => {
    const formatId = event.target.value
    onFormatChange(formatId)
  }

  return (
    <div className={styles.formatSelector}>
      <label htmlFor="format-select" className={styles.label}>
        Выберите формат события:
      </label>
      <select id="format-select" value={selectedFormatId || ''} onChange={handleFormatChange} className={styles.select}>
        <option value="">-- Выберите формат --</option>
        {formats?.map((format) => (
          <option key={format.public_id} value={format.public_id}>
            {format.title}
          </option>
        ))}
      </select>
    </div>
  )
}

export default FormatSelector
