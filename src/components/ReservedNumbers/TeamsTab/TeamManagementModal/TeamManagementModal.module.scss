@import '../../../Scss/Mixins.scss';
@import '../../../Scss/Variables.scss';

.wrapper {
  position: relative;
  max-width: 50rem;
  width: 100%;
  padding: 2.5rem;
  background-color: #151515;
  border-radius: 1rem;
  border: 1px solid #2a2b2e;

  &White {
    background-color: #ffffff;
    border-color: #dae3ef;
    color: #111113;
  }
}

.closeBtn {
  position: absolute;
  top: 0.625rem;
  right: 0.625rem;
  width: 1.875rem;
  height: 1.875rem;
  padding: 0;
  cursor: pointer;
  background-color: transparent;
  border: none;

  @include pseudo-cross-btn($r: 45deg);

  .wrapperWhite & {
    &::before,
    &::after {
      background-color: #111113;
    }
  }
}

.title {
  font-family: 'DIN Pro';
  font-size: 1.75rem;
  font-weight: 900;
  line-height: 1.222;
  margin-bottom: 2rem;
  text-align: center;
}

.tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #2a2b2e;

  .wrapperWhite & {
    border-bottom-color: #dae3ef;
  }
}

.tab {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 400;
  transition: color 0.3s ease;

  .wrapperWhite & {
    color: rgba(17, 17, 19, 0.6);
  }

  &:hover {
    color: rgba(255, 255, 255, 0.8);

    .wrapperWhite & {
      color: rgba(17, 17, 19, 0.8);
    }
  }

  &Active {
    color: #76b72a;
    border-bottom: 2px solid #76b72a;
  }
}

.content {
  min-height: 200px;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.description {
  font-size: 1rem;
  line-height: 1.25;
  margin: 0;
}

.warning {
  font-size: 0.875rem;
  line-height: 1.25;
  color: #ff6b6b;
  margin: 0;
  font-weight: 500;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input,
.select {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  line-height: 1.25;
  color: white;
  background-color: transparent;
  border: 1px solid #323232;
  border-radius: 0.5rem;
  outline: none;
  transition: border-color 0.3s ease;

  .wrapperWhite & {
    color: #111113;
    border-color: #dae3ef;
  }

  &:focus {
    border-color: #76b72a;
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);

    .wrapperWhite & {
      color: rgba(17, 17, 19, 0.5);
    }
  }
}

.select {
  cursor: pointer;

  option {
    background-color: #151515;
    color: white;

    .wrapperWhite & {
      background-color: #ffffff;
      color: #111113;
    }
  }
}

.btn {
  @include btn45;
  padding: 0 2rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.btnPrimary {
  background: linear-gradient(270deg, #76b72a, #5a9e1f);
  color: white;

  &:hover:not(:disabled) {
    background: linear-gradient(270deg, #5a9e1f, #76b72a);
  }
}

.btnSecondary {
  background: linear-gradient(270deg, #666666, #555555);
  color: white;

  &:hover:not(:disabled) {
    background: linear-gradient(270deg, #555555, #666666);
  }
}

.btnDanger {
  background: linear-gradient(270deg, #ff6b6b, #e55555);
  color: white;

  &:hover:not(:disabled) {
    background: linear-gradient(270deg, #e55555, #ff6b6b);
  }
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  justify-content: center;

  .btn {
    flex: 1;
    max-width: 200px;
  }
}

@media (max-width: $tabletWidth) {
  .wrapper {
    padding: 2rem 1.5rem;
  }

  .title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .tabs {
    flex-direction: column;
    border-bottom: none;
  }

  .tab {
    border-bottom: 1px solid #2a2b2e;

    .wrapperWhite & {
      border-bottom-color: #dae3ef;
    }

    &:last-child {
      border-bottom: none;
    }

    &Active {
      border-bottom-color: #76b72a;
      border-left: 3px solid #76b72a;
    }
  }

  .buttonGroup {
    flex-direction: column;

    .btn {
      max-width: none;
    }
  }
}
