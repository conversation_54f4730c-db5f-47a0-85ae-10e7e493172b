import { useState } from 'react'
import { useSelector } from 'react-redux'

import UniversalPopup from '@/components/Popups/UniversalPopup/UniversalPopup'
import { useDeleteTeam } from '@/features/teams/api/deleteTeam'
import { useSwapTeamNumbers } from '@/features/teams/api/swapTeamNumbers'
import { useUpdateTeamNumber } from '@/features/teams/api/updateTeamNumber'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import styles from './TeamManagementModal.module.scss'

const TeamManagementModal = ({ team, onClose, allTeams = [] }) => {
  const theme = useSelector((state) => getThemeMemReg(state))
  const [activeTab, setActiveTab] = useState('changeNumber')
  const [newNumber, setNewNumber] = useState('')
  const [swapTeamId, setSwapTeamId] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const { toasts, removeToast } = useToast()
  const { mutate: updateTeamNumber, isLoading: isUpdating } = useUpdateTeamNumber()
  const { mutate: swapTeamNumbers, isLoading: isSwapping } = useSwapTeamNumbers()
  const { mutate: deleteTeam, isLoading: isDeleting } = useDeleteTeam()

  const handleChangeNumber = () => {
    if (!newNumber.trim()) return

    updateTeamNumber(
      { publicId: team.public_id, number: parseInt(newNumber) },
      {
        onSuccess: () => {
          onClose()
        },
        onError: (error) => {
          console.error('Ошибка при смене номера:', error)
        },
      }
    )
  }

  const handleSwapNumbers = () => {
    if (!swapTeamId) return

    swapTeamNumbers(
      {
        firstTeamPublicId: team.public_id,
        secondTeamPublicId: swapTeamId,
      },
      {
        onSuccess: () => {
          onClose()
        },
        onError: (error) => {
          console.error('Ошибка при смене номеров:', error)
        },
      }
    )
  }

  const handleDeleteTeam = () => {
    deleteTeam(team.public_id, {
      onSuccess: () => {
        onClose()
      },
      onError: (error) => {
        console.error('Ошибка при удалении команды:', error)
      },
    })
  }

  const availableTeams = allTeams.filter((t) => t.public_id !== team.public_id)

  return (
    <>
      <UniversalPopup>
        <div className={`${styles.wrapper} ${theme ? styles.wrapperWhite : ''}`}>
          <button className={styles.closeBtn} onClick={onClose} type="button" aria-label="Закрыть" />

        <h2 className={styles.title}>Управление командой #{team.number}</h2>

        <div className={styles.tabs}>
          <button
            className={`${styles.tab} ${activeTab === 'changeNumber' ? styles.tabActive : ''}`}
            onClick={() => setActiveTab('changeNumber')}
            type="button"
          >
            Сменить номер
          </button>
          <button
            className={`${styles.tab} ${activeTab === 'swapNumbers' ? styles.tabActive : ''}`}
            onClick={() => setActiveTab('swapNumbers')}
            type="button"
          >
            Поменять местами
          </button>
          <button
            className={`${styles.tab} ${activeTab === 'delete' ? styles.tabActive : ''}`}
            onClick={() => setActiveTab('delete')}
            type="button"
          >
            Удалить команду
          </button>
        </div>

        <div className={styles.content}>
          {activeTab === 'changeNumber' && (
            <div className={styles.section}>
              <p className={styles.description}>Введите новый номер для команды:</p>
              <div className={styles.inputGroup}>
                <input
                  type="number"
                  value={newNumber}
                  onChange={(e) => setNewNumber(e.target.value)}
                  placeholder="Новый номер"
                  className={styles.input}
                  min="1"
                />
              </div>
              <button
                className={`${styles.btn} ${styles.btnPrimary}`}
                onClick={handleChangeNumber}
                disabled={!newNumber.trim() || isUpdating}
                type="button"
              >
                {isUpdating ? 'Сохранение...' : 'Сменить номер'}
              </button>
            </div>
          )}

          {activeTab === 'swapNumbers' && (
            <div className={styles.section}>
              <p className={styles.description}>Выберите команду для обмена номерами:</p>
              <div className={styles.inputGroup}>
                <select value={swapTeamId} onChange={(e) => setSwapTeamId(e.target.value)} className={styles.select}>
                  <option value="">-- Выберите команду --</option>
                  {availableTeams.map((t) => (
                    <option key={t.public_id} value={t.public_id}>
                      Команда #{t.number}
                    </option>
                  ))}
                </select>
              </div>
              <button
                className={`${styles.btn} ${styles.btnPrimary}`}
                onClick={handleSwapNumbers}
                disabled={!swapTeamId || isSwapping}
                type="button"
              >
                {isSwapping ? 'Обмен...' : 'Поменять местами'}
              </button>
            </div>
          )}

          {activeTab === 'delete' && (
            <div className={styles.section}>
              {!showDeleteConfirm ? (
                <>
                  <p className={styles.description}>Вы уверены, что хотите удалить команду #{team.number}?</p>
                  <p className={styles.warning}>Это действие нельзя отменить!</p>
                  <button
                    className={`${styles.btn} ${styles.btnDanger}`}
                    onClick={() => setShowDeleteConfirm(true)}
                    type="button"
                  >
                    Удалить команду
                  </button>
                </>
              ) : (
                <>
                  <p className={styles.description}>Подтвердите удаление команды #{team.number}:</p>
                  <div className={styles.buttonGroup}>
                    <button
                      className={`${styles.btn} ${styles.btnSecondary}`}
                      onClick={() => setShowDeleteConfirm(false)}
                      type="button"
                    >
                      Отмена
                    </button>
                    <button
                      className={`${styles.btn} ${styles.btnDanger}`}
                      onClick={handleDeleteTeam}
                      disabled={isDeleting}
                      type="button"
                    >
                      {isDeleting ? 'Удаление...' : 'Подтвердить удаление'}
                    </button>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </UniversalPopup>
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </>
  )
}

export default TeamManagementModal
